#!/usr/bin/env ruby

# Script to create a demo patient with comprehensive test data
# Usage: rails runner create_demo_patient.rb

puts "🏥 Creating demo patient with comprehensive test data..."

# Create demo user first
demo_user = User.find_or_create_by!(email: "<EMAIL>") do |user|
  user.first_name = "Demo"
  user.last_name = "Patient"
  user.phone_number = "+905551234999"
  user.birth_date = Date.new(1990, 5, 15) # 34 years old
  user.password = "demopass123"
  user.password_confirmation = "demopass123"
  user.locale = "tr"
  user.timezone = "Europe/Istanbul"
  user.verified_at = Time.current
end

puts "✅ Created demo user: #{demo_user.full_name} (#{demo_user.email})"

# Create patient profile
demo_patient = Patient.find_or_create_by!(user: demo_user) do |patient|
  patient.emergency_contact_name = "Eş - Ahmet Patient"
  patient.emergency_contact_phone = "+905551234998"
  patient.blood_type = "A+"
  patient.pregnancy_count = 2
  patient.birth_count = 1
  patient.smoking_status = :non_smoker
  patient.alcohol_consumption = :occasionally
end

puts "✅ Created demo patient profile"
puts "   Medical Record Number: #{demo_patient.medical_record_number}"
puts "   Blood Type: #{demo_patient.blood_type}"
puts "   Age: #{demo_patient.age} years old"

# Create health profile
health_profile = HealthProfile.find_or_create_by!(patient: demo_patient) do |profile|
  profile.height = 165.0 # cm
  profile.weight = 62.5 # kg
  profile.allergies = "Seasonal pollen, dust mites"
  profile.chronic_conditions = "Mild Hypertension, Seasonal Allergies"
  profile.medications = "Lisinopril 10mg daily, Antihistamine as needed"
  profile.family_history = "Diabetes (Mother), Heart Disease (Father), Breast Cancer (Maternal Grandmother)"
  profile.lifestyle_notes = "Exercise 3-4 times per week, Mediterranean diet, 7-8 hours sleep, moderate stress level"
end

puts "✅ Created health profile"
puts "   BMI: #{health_profile.bmi} (#{health_profile.bmi_category})"
puts "   Chronic Conditions: #{health_profile.chronic_conditions}"
puts "   Allergies: #{health_profile.allergies}"

# Create cycle tracker data (last 3 cycles)
puts "📅 Creating menstrual cycle tracking data..."

cycle_dates = [
  { start: 3.months.ago.to_date, length: 28, period: 5 },
  { start: 2.months.ago.to_date, length: 30, period: 4 },
  { start: 1.month.ago.to_date, length: 29, period: 5 }
]

cycle_dates.each_with_index do |cycle_data, index|
  cycle = CycleTracker.find_or_create_by!(
    patient: demo_patient,
    cycle_start_date: cycle_data[:start]
  ) do |tracker|
    tracker.cycle_length = cycle_data[:length]
    tracker.period_length = cycle_data[:period]
    tracker.ovulation_date = cycle_data[:start] + 14.days
    tracker.next_period_predicted = cycle_data[:start] + cycle_data[:length].days
    tracker.flow_intensity = rand(1..3) # light to heavy
    tracker.symptoms = [
      "Mild cramping",
      "Mood changes",
      "Breast tenderness"
    ].sample(rand(1..3))
    tracker.mood = rand(1..5) # 1-5 scale
    tracker.temperature = 36.5 + rand(-0.5..0.5).round(1)
    tracker.notes = "Cycle #{index + 1} - Normal flow and symptoms"
  end
  
  puts "   ✓ Cycle #{index + 1}: #{cycle.cycle_start_date} (#{cycle.cycle_length} days)"
end

# Create pregnancy tracker (current pregnancy)
puts "🤱 Creating pregnancy tracking data..."

pregnancy = PregnancyTracker.find_or_create_by!(
  patient: demo_patient,
  active: true
) do |tracker|
  tracker.conception_date = 12.weeks.ago.to_date
  tracker.due_date = tracker.conception_date + 280.days # 40 weeks
  tracker.current_week = 12
  tracker.current_trimester = 1
  tracker.weight_gain = 2.5 # kg
  tracker.fundal_height = 12.0 # cm
  tracker.fetal_heart_rate = 150 # bpm
  tracker.blood_pressure_systolic = 115
  tracker.blood_pressure_diastolic = 75
  tracker.glucose_level = 85.0
  tracker.iron_level = 12.5
  tracker.symptoms = [
    "Morning sickness (mild)",
    "Fatigue",
    "Breast tenderness",
    "Frequent urination"
  ]
  tracker.notes = "First trimester progressing normally. Morning sickness improving."
  tracker.risk_factors = []
end

puts "✅ Created pregnancy tracker"
puts "   Current Week: #{pregnancy.current_week}"
puts "   Due Date: #{pregnancy.due_date}"
puts "   Trimester: #{pregnancy.current_trimester}"

# Create some health metrics
puts "📊 Creating health metrics..."

metrics_data = [
  { type: "weight", value: 62.5, unit: "kg", date: 1.week.ago },
  { type: "blood_pressure_systolic", value: 115, unit: "mmHg", date: 1.week.ago },
  { type: "blood_pressure_diastolic", value: 75, unit: "mmHg", date: 1.week.ago },
  { type: "heart_rate", value: 72, unit: "bpm", date: 3.days.ago },
  { type: "temperature", value: 36.7, unit: "°C", date: 1.day.ago }
]

metrics_data.each do |metric|
  health_metric = HealthMetric.find_or_create_by!(
    patient: demo_patient,
    metric_type: metric[:type],
    recorded_at: metric[:date]
  ) do |hm|
    hm.trackable = pregnancy if metric[:type].include?("blood_pressure") || metric[:type] == "heart_rate"
    hm.value = metric[:value]
    hm.unit = metric[:unit]
    hm.source = "manual_entry"
    hm.notes = "Demo data for testing"
  end
  
  puts "   ✓ #{metric[:type]}: #{metric[:value]} #{metric[:unit]}"
end

puts "\n🎉 Demo patient created successfully!"
puts "\n📋 Summary:"
puts "   Email: #{demo_user.email}"
puts "   Password: demopass123"
puts "   Name: #{demo_user.full_name}"
puts "   Age: #{demo_patient.age} years"
puts "   Medical Record: #{demo_patient.medical_record_number}"
puts "   Current Status: #{pregnancy.current_week} weeks pregnant"
puts "   BMI: #{health_profile.bmi} (#{health_profile.bmi_category})"
puts "\n🔐 Login credentials:"
puts "   Email: <EMAIL>"
puts "   Password: demopass123"
puts "\n✨ This patient has comprehensive demo data including:"
puts "   • Complete user profile with verification"
puts "   • Patient medical information"
puts "   • Health profile with BMI calculation"
puts "   • 3 months of menstrual cycle data"
puts "   • Active pregnancy tracking (12 weeks)"
puts "   • Recent health metrics"
puts "   • Emergency contact information"
puts "   • Medical history and family history"
